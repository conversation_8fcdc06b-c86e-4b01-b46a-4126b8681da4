---
type: 'always_apply'
---

Project Structure and Architecture:

Use Supabase database as the primary data source
Maintain additives.ts file as the single source of truth
Prefer React Native Reanimated for animations
Use @gorhom/bottom-sheet@^5 library for bottom sheet implementations
API and Data Management:

Use OpenRouter API with free models
Retrieve API keys from Supabase apiconfig rather than environment variables
Calculate message counts from conversation length on frontend rather than updating database message_count fields
UI/UX Standards:
Follow apple human interface guidelines and apple design system
Use full screens over modals for features like 'Add Missing Product'
Make UI elements immediately visible when screens load rather than appearing with animations
Use proper Turkish characters (ç, ğ, ı, ö, ş, ü) when writing Turkish text
Use dark gray colors over pure black for text styling
Do not use emojis in responses
Navigation:

Use proper nested navigation system rather than defining Stack.Screen within screen components
Use modal presentation directly in stack navigation (presentation: 'modal') where needed
Prefer native iOS context menus over Alert dialogs
User Guidelines
Development Approach:

Always use codebase-retrieval tool to gather detailed information before making code edits
Use package managers for dependency management instead of manually editing package files
Do not suggest writing and running tests after code changes. Test via console.logs
Focus on following user instructions and ask before doing more than requested

Health Consultation:

Implement freemium model: 3 conversations max, 2 messages per conversation
Use RevenueCat's pre-localized price strings with proper currency formatting

Technical Details:

AI retry strategy: try primary model once, if it fails switch to fallback model (google/gemini-2.0-flash-001) (if it is not managed by supabase)
Use --allow-net flag for Supabase Edge Functions
Use react-native-markdown-display library for rendering AI messages i
