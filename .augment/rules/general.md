---
type: 'always_apply'
---

Rules (Kurallar)
Proje <PERSON>ı ve Mimari:

Supabase veritabanını birincil veri kaynağı olarak kullan
additives.ts dosyasını tek gerçeklik kaynağı olarak koru
React Native Reanimated'ı animasyonlar için tercih et
@gorhom/bottom-sheet@^5 kütüphanesini bottom sheet implementasyonları için kullan
API ve Veri Yönetimi:

OpenRouter API'yi ücretsiz modeller ile kullan
API anahtarlarını environment variables yerine Supabase apiconfig'den al
Mesaj sayılarını veritabanı alanları yerine frontend'de conversation uzunluğundan hesapla
Fuzzy matching için tek karakter farkına izin ver
UI/UX Standartları:

Modal yerine tam ekran kullan (Add Missing Product gibi özellikler için)
Animasyonlar yerine UI elementlerini hemen görünür yap
Türkçe metinlerde doğru karakterleri kullan (ç, ğ, ı, ö, ş, ü)
Saf siyah yerine koyu gri renkler kullan
Emoji kullanma
Navigasyon:

Düzgün nested navigation sistemi kullan
Modal presentation'ı stack navigation'da direkt kullan
iOS context menu'leri Alert dialog'lar yerine tercih et
User Guidelines (Kullanıcı Kılavuzları)
Geliştirme Yaklaşımı:

Kod değişikliklerinden önce her zaman codebase-retrieval tool'unu kullanarak detaylı bilgi topla
Package manager'ları kullan, package dosyalarını manuel düzenleme
Test yazma ve çalıştırmayı öner
Kullanıcının istediğinden fazlasını yapma, takip görevleri için sor
Sağlık Konsültasyonu:

Freemium model: maksimum 3 konuşma, konuşma başına 2 mesaj
Abonelik fiyatlandırması: aylık $0.99, yıllık $2.99
RevenueCat'in önceden lokalize edilmiş fiyat stringlerini kullan
Teknik Detaylar:

React Native ExecuTorch v0.4.0 kullan
AI retry stratejisi: birincil model başarısız olursa google/gemini-2.0-flash-001'e geç
Supabase Edge Functions için --allow-net flag kullan
react-native-markdown-display kütüphanesini AI mesajları için kullan
